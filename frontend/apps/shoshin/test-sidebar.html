<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Test</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #111827;
            color: white;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .outermost-sidebar {
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            background: #111827;
            border-right: 1px solid #374151;
            transition: all 0.3s ease;
            z-index: 20;
            width: 64px;
        }
        
        .outermost-sidebar:hover {
            width: 256px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .content-area {
            margin-left: 64px;
            display: flex;
            flex: 1;
        }
        
        .secondary-sidebar {
            background: #1f2937;
            border-right: 1px solid #374151;
            transition: all 0.3s ease;
            z-index: 10;
            width: 64px;
        }
        
        .secondary-sidebar.expanded {
            width: 256px;
        }
        
        .main-content {
            flex: 1;
            background: #0f172a;
            padding: 20px;
        }
        
        .sidebar-content {
            padding: 16px;
        }
        
        .icon {
            width: 32px;
            height: 32px;
            background: #6366f1;
            border-radius: 4px;
            margin-bottom: 16px;
        }
        
        .text {
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .outermost-sidebar:hover .text {
            opacity: 1;
        }
        
        .secondary-sidebar.expanded .text {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="outermost-sidebar">
            <div class="sidebar-content">
                <div class="icon"></div>
                <div class="text">Outermost Sidebar</div>
                <div class="text">Should expand on hover</div>
                <div class="text">And overlay secondary sidebar</div>
            </div>
        </div>
        
        <div class="content-area">
            <div class="secondary-sidebar" id="secondarySidebar">
                <div class="sidebar-content">
                    <div class="icon" style="background: #f59e0b;"></div>
                    <div class="text">Secondary Sidebar</div>
                    <div class="text">Should NOT expand when outermost is hovered</div>
                </div>
            </div>
            
            <div class="main-content">
                <h1>Sidebar Behavior Test</h1>
                <p>Expected behavior:</p>
                <ul>
                    <li>Hover over the leftmost (outermost) sidebar - it should expand to 256px width</li>
                    <li>The secondary sidebar (orange) should stay at 64px width</li>
                    <li>The outermost sidebar should overlay on top of the secondary sidebar</li>
                    <li>Only the outermost sidebar should respond to hover</li>
                </ul>
                
                <button onclick="toggleSecondary()">Toggle Secondary Sidebar (for testing)</button>
            </div>
        </div>
    </div>
    
    <script>
        function toggleSecondary() {
            const sidebar = document.getElementById('secondarySidebar');
            sidebar.classList.toggle('expanded');
        }
    </script>
</body>
</html>
